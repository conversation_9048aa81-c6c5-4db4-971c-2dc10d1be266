# DeepSeek加密货币永续合约全自动量化交易系统 设计文档

## 概述

### 设计目标
本系统旨在构建一个基于DeepSeek AI模型的加密货币永续合约全自动量化交易系统，实现智能化的交易决策和风险管理。系统采用模块化设计，确保高可用性、可扩展性和安全性。

### 范围
- 支持欧易交易所的模拟盘和实盘交易
- 集成DeepSeek AI模型进行智能决策
- 提供人性化的桌面应用界面
- 实现多时间周期技术分析
- 支持全自动化的开仓和持仓管理

### 关键约束
- 使用Python作为主要开发语言
- 采用RESTful API与交易所通信，不使用WebSocket
- 使用SQLite数据库存储配置，不存储交易数据
- 支持保证金交易和全仓模式
- 仅支持单向持仓策略

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "前端层"
        UI[PyWebview桌面应用]
    end
    
    subgraph "应用层"
        API[内置API接口]
        CONFIG[配置管理器]
        SCHEDULER[任务调度器]
    end
    
    subgraph "业务逻辑层"
        OPEN_ENGINE[AI开仓引擎]
        HOLD_ENGINE[AI持仓引擎]
        RISK_MGR[风险管理器]
        TRADE_EXEC[交易执行器]
    end
    
    subgraph "数据处理层"
        MARKET_DATA[市场数据处理器]
        TECH_ANALYSIS[技术分析引擎]
        AI_CLIENT[DeepSeek AI客户端]
    end
    
    subgraph "基础设施层"
        CCXT_CLIENT[CCXT交易所客户端]
        DB[SQLite数据库]
        LOGGER[日志系统]
    end
    
    subgraph "外部服务"
        OKX[欧易交易所]
        DEEPSEEK[DeepSeek API]
    end
    
    UI --> API
    API --> CONFIG
    API --> SCHEDULER
    
    SCHEDULER --> OPEN_ENGINE
    SCHEDULER --> HOLD_ENGINE
    
    OPEN_ENGINE --> TECH_ANALYSIS
    HOLD_ENGINE --> TECH_ANALYSIS
    OPEN_ENGINE --> AI_CLIENT
    HOLD_ENGINE --> AI_CLIENT
    
    OPEN_ENGINE --> RISK_MGR
    HOLD_ENGINE --> RISK_MGR
    RISK_MGR --> TRADE_EXEC
    
    TECH_ANALYSIS --> MARKET_DATA
    MARKET_DATA --> CCXT_CLIENT
    TRADE_EXEC --> CCXT_CLIENT
    
    CCXT_CLIENT --> OKX
    AI_CLIENT --> DEEPSEEK
    
    CONFIG --> DB
    LOGGER --> DB
```

### 部署架构
- **单机部署**：所有组件运行在同一台机器上
- **进程模型**：主进程运行PyWebview界面，后台线程处理交易逻辑
- **数据存储**：本地SQLite数据库存储配置和日志
- **网络通信**：通过HTTPS与外部API通信

### 关键技术选型

| 组件 | 技术选择 | 理由 |
|------|----------|------|
| 前端框架 | PyWebview + HTML/CSS/JS | 跨平台桌面应用，开发效率高 |
| 后端语言 | Python 3.8+ | 丰富的金融库支持，开发效率高 |
| 交易所接口 | CCXT | 统一的交易所API接口 |
| 技术分析 | TA-Lib | 成熟的技术分析库 |
| AI模型 | DeepSeek API | 强大的推理能力 |
| 数据库 | SQLite3 | 轻量级，无需额外部署 |
| 任务调度 | APScheduler | Python原生调度库 |

## 组件和接口

### 核心组件详细设计

#### 1. PyWebview桌面应用 (UI层)
**功能职责：**
- 提供用户交互界面
- 显示实时交易状态
- 配置系统参数

**关键接口：**
```python
class WebviewAPI:
    def get_system_status(self) -> dict
    def get_trading_pairs(self) -> list
    def update_global_settings(self, settings: dict) -> bool
    def start_trading(self, pairs: list) -> bool
    def stop_trading(self) -> bool
    def get_positions(self) -> list
    def get_trading_history(self) -> list
```

#### 2. AI开仓引擎 (AIOpenEngine)
**功能职责：**
- 分析市场机会
- 决定开仓时机和方向
- 生成开仓信号

**关键接口：**
```python
class AIOpenEngine:
    def analyze_market(self, symbol: str) -> dict
    def should_open_position(self, analysis: dict) -> bool
    def get_position_direction(self, analysis: dict) -> str  # 'long' or 'short'
    def calculate_position_size(self, confidence: float) -> float
```

**AI提示词模板：**
```
你是一个专业的加密货币交易分析师。基于以下技术指标数据，分析市场趋势并给出开仓建议：

技术指标数据：
- 1分钟周期：{indicators_1m}
- 5分钟周期：{indicators_5m}  
- 15分钟周期：{indicators_15m}
- 1小时周期：{indicators_1h}

请分析：
1. 当前市场趋势方向
2. 入场时机是否合适
3. 建议开仓方向（多头/空头）
4. 置信度评分（0-100）
5. 详细分析理由

返回JSON格式：
{
  "direction": "long/short/none",
  "confidence": 85,
  "reasoning": "详细分析理由"
}
```

#### 3. AI持仓引擎 (AIHoldEngine)
**功能职责：**
- 监控现有持仓
- 决定平仓时机
- 管理止盈止损

**关键接口：**
```python
class AIHoldEngine:
    def analyze_position(self, position: dict, market_data: dict) -> dict
    def should_close_position(self, analysis: dict) -> bool
    def update_stop_loss(self, position: dict, analysis: dict) -> float
    def update_take_profit(self, position: dict, analysis: dict) -> float
```

**AI提示词模板：**
```
你是一个专业的仓位管理专家。基于当前持仓信息和市场数据，给出持仓管理建议：

当前持仓：
- 交易对：{symbol}
- 方向：{direction}
- 开仓价格：{entry_price}
- 当前价格：{current_price}
- 未实现盈亏：{unrealized_pnl}
- 持仓时间：{hold_duration}

技术指标：{technical_indicators}

请分析：
1. 是否应该继续持有
2. 是否需要调整止损位
3. 是否需要调整止盈位
4. 平仓置信度（0-100）
5. 详细分析理由

返回JSON格式：
{
  "action": "hold/close",
  "confidence": 75,
  "stop_loss": 45000,
  "take_profit": 52000,
  "reasoning": "详细分析理由"
}
```
